

from tools.inference import Detector, MultiTaskDetector
from econn.utils.config import cfg, update_config
import cv2
import numpy as np
import os
import torch
import yaml
import sys

# 添加pixel_to_physical_py路径
sys.path.append('pixel_to_physical_py')
from src.detection_processor import DetectionProcessor, DetectionResult, BBox

# demo = r"D:\Videos\YDXJ0175_0226.mp4"
# demo = r"D:\Dataset\coco\val2017"
# demo = r'D:\Dataset\eco_indoor_2020_old\val2019'
demo = r"webcam"
camera_id = 0
config_path = r"exp/GFL_0503_gpu2/train_config.yml"
model_path = r"exp/GFL_0503_gpu2/model_best_score/model_best_score.pth"

update_config(cfg, config_path)
class_names = cfg.class_names
class_names = ['Trash can', 'Charging dock', 'Cleaning cloth', 'Rug', 'Shoes',
              'Wire', 'Sliding rail', 'Wheels']
if cfg.model.architecture == 'MultiTask':
    detector = MultiTaskDetector(cfg, model_path, device='cuda:0')
else:
    detector = Detector(cfg, model_path, 'cuda:0')

# 初始化物理距离检测处理器
try:
    with open("pixel_to_physical_py/config/size_ranges.yaml", 'r') as f:
        size_config = yaml.safe_load(f)

    physical_processor = DetectionProcessor()
    physical_processor.set_size_ranges_config(size_config)
    distance_table_path = "pixel_to_physical_py/config/distance_table"
    print("物理距离检测处理器初始化成功")
except Exception as e:
    print(f"物理距离检测处理器初始化失败: {e}")
    physical_processor = None


torch.backends.cudnn.enabled = True
torch.backends.cudnn.benchmark = True

# # 创建类别名称映射字典
CLASS_NAME_MAPPING = {
    'trash can': 'bin',
    'charging dock': 'seatbase',  # 充电桩可能类似座椅底座
    'cleaning cloth': 'cloth',
    'rug': 'rug',
    'shoes': 'shoe',
    'wire': 'wire',
    'sliding rail': 'rail',
    'wheels': 'wheel'
}
def load_camera_params(yaml_path):
    """从 YAML 文件加载相机参数"""
    with open(yaml_path, "r") as f:
        calib = yaml.safe_load(f)

    cam = calib["camera_parameters"]
    fx = cam["camera_matrix"]["fx"]
    fy = cam["camera_matrix"]["fy"]
    cx = cam["camera_matrix"]["cx"]
    cy = cam["camera_matrix"]["cy"]

    camera_matrix = np.array([
        [fx, 0, cx],
        [0, fy, cy],
        [0,  0,  1]
    ], dtype=np.float64)

    radial = cam["distortion_coefficients"]["radial"]
    tangential = cam["distortion_coefficients"]["tangential"]

    dist_coeffs = np.array([
        radial["k1"],
        radial["k2"],
        tangential["p1"],
        tangential["p2"],
        radial["k3"]
    ], dtype=np.float64)

    return camera_matrix, dist_coeffs

def convert_dets_to_detection_results(dets, class_names):
    """
    将检测器输出的dets格式转换为DetectionResult列表

    Args:
        dets: 检测器输出的检测结果，格式为 {label: [[x1, y1, x2, y2, confidence], ...]}
        class_names: 类别名称列表

    Returns:
        List[DetectionResult]: 转换后的检测结果列表
    """
    detection_results = []

    if not dets or len(dets) == 0:
        return detection_results

    # dets[0] 是第一张图片的检测结果
    det_dict = dets[0] if isinstance(dets, list) else dets

    for label_id, bboxes in det_dict.items():
        if not bboxes:  # 如果该类别没有检测结果
            continue

        # 获取类别名称
        if label_id <= len(class_names):
            label_name = class_names[label_id - 1]  # label_id 是1-based
        else:
            label_name = f"class_{label_id}"

        # 转换每个检测框
        for bbox in bboxes:
            if len(bbox) >= 5:  # 确保有足够的元素 [x1, y1, x2, y2, confidence]
                x1, y1, x2, y2, confidence = bbox[:5]

                # 过滤置信度过低的检测结果
                if confidence < 0.3:  # 可以调整这个阈值
                    continue

                # 创建BBox对象
                bbox_obj = BBox(
                    x1=int(x1),
                    y1=int(y1),
                    x2=int(x2),
                    y2=int(y2)
                )

                # 映射类别名称
                mapped_label = CLASS_NAME_MAPPING.get(label_name.lower(), label_name.lower())

                # 创建DetectionResult对象
                det_result = DetectionResult(
                    bbox=bbox_obj,
                    label=mapped_label,
                    confidence=float(confidence)
                )

                detection_results.append(det_result)

    return detection_results

def show_result_with_distance(img, dets, detection_results, class_names, score_thres=0.3):
    """
    显示检测结果并包含物理距离信息，不同类别使用不同颜色

    Args:
        img: 原始图像
        dets: 原始检测结果
        detection_results: 包含物理距离的检测结果列表
        class_names: 类别名称列表
        score_thres: 置信度阈值

    Returns:
        img: 绘制了检测框和距离信息的图像
    """
    # 为不同类别定义不同颜色
    colors = [
        (255, 0, 0),    # 红色 - class 1
        (0, 255, 0),    # 绿色 - class 2
        (0, 0, 255),    # 蓝色 - class 3
        (255, 255, 0),  # 青色 - class 4
        (255, 0, 255),  # 紫色 - class 5
        (0, 255, 255),  # 黄色 - class 6
        (255, 128, 0),  # 橙色 - class 7
        (128, 0, 255),  # 紫红色 - class 8
        (0, 255, 128),  # 春绿色 - class 9
        (128, 255, 0)   # 黄绿色 - class 10
    ]
    # 在去畸变图像上绘制范围框
    x_min, x_max = IMGX_RANGE
    y_min, y_max = IMGY_RANGE
    
    # 绘制矩形框 (红色，线宽2)
    cv2.rectangle(img, 
                (x_min, y_min), 
                (x_max, y_max), 
                (128, 255, 0), 2)
    # 创建一个字典来快速查找物理距离信息
    distance_info = {}
    for det_result in detection_results:
        key = (det_result.bbox.x1, det_result.bbox.y1, det_result.bbox.x2, det_result.bbox.y2)
        distance_info[key] = det_result

    # 绘制检测框和距离信息
    if isinstance(dets, list) and len(dets) > 0:
        det_dict = dets[0]
    else:
        det_dict = dets

    for label_id, bboxes in det_dict.items():
        if not bboxes:
            continue

        # 为当前类别选择颜色
        color_idx = (label_id - 1) % len(colors)
        color = colors[color_idx]

        for bbox in bboxes:
            if len(bbox) >= 5 and bbox[4] > score_thres:
                x1, y1, x2, y2, confidence = bbox[:5]
                x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                if confidence <0.3:
                    continue
                # 绘制检测框
                cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)

                # 获取类别名称
                if label_id <= len(class_names):
                    label_name = class_names[label_id - 1]
                else:
                    label_name = f"class_{label_id}"

                # 查找对应的物理距离信息
                key = (x1, y1, x2, y2)
                det_result = distance_info.get(key)

                # 准备显示文本
                if det_result and det_result.physical_distance > 0:
                    text = f"{label_name}: {det_result.physical_distance:.1f}cm"
                else:
                    text = f"{label_name}: {confidence:.2f}"

                # 绘制文本背景
                text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                cv2.rectangle(img, (x1, y1-25), (x1 + text_size[0], y1), color, -1)

                # 绘制文本
                cv2.putText(img, text, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

    return img


# 加载相机标定参数
calib_yaml_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"  # 修改为你的标定文件路径
camera_matrix, dist_coeffs = load_camera_params(calib_yaml_path)

# 初始化去畸变映射
h, w = 720, 1280  # 与摄像头设置的分辨率一致
newcameramtx, roi = cv2.getOptimalNewCameraMatrix(
    camera_matrix, dist_coeffs, (w, h), alpha=0, newImgSize=(w, h)
)
mapx, mapy = cv2.initUndistortRectifyMap(
    camera_matrix, dist_coeffs, None, newcameramtx, (w, h), cv2.CV_32FC1
)
IMGX_RANGE = [30, 1250]  # Example values, adjust as needed
IMGY_RANGE = [400, 720]

image_ext = ['.jpg', '.jpeg', '.webp', '.bmp', '.png']
video_ext = ['mp4', 'mov', 'avi', 'mkv']

if demo == 'webcam' or demo[demo.rfind('.') + 1:].lower() in video_ext:
    camera = cv2.VideoCapture(0, cv2.CAP_V4L2)
    # 指定分辨率和编码格式
    camera.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    camera.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
    camera.set(cv2.CAP_PROP_FRAME_HEIGHT, 720)
    if not camera.isOpened():
        print("无法打开摄像头")
        exit()
    print('Press "Esc", "q" or "Q" to exit.')
    while True:
        ret_val, img = camera.read()
        if not ret_val:
            print("无法读取帧")
            break
        
        # 应用去畸变
        img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        

        cv2.imshow('摄像头画面', img)

        if cv2.waitKey(1) & 0xFF == ord('q'):  # 按 'q' 键退出
            break

        # img = cv2.remap(img, mapx, mapy, cv2.INTER_LINEAR)
        dets, meta = detector.inference(img)
        # print("原始检测结果:", dets)

        # 处理物理距离计算
        detection_results = []
        if physical_processor is not None:
            try:
                # 转换检测结果格式
                detection_results = convert_dets_to_detection_results(dets, class_names)

                print(f"\n检测到 {len(detection_results)} 个目标")

                # 处理每个检测结果
                for i, det_result in enumerate(detection_results):
                    print(f"\n--- 处理目标 {i+1}: {det_result.label} ---")

                    physical_processor.process_detection_result(det_result, distance_table_path)

                    if det_result.physical_distance > 0:
                        print(f"目标 {det_result.label}:")
                        print(f"  物理距离: {det_result.physical_distance:.2f} cm")
                        print(f"  左下角距离: {det_result.left_distance:.2f} cm")
                        print(f"  右下角距离: {det_result.right_distance:.2f} cm")
                    else:
                        print(f"目标 {det_result.label}: 无法计算物理距离")

            except Exception as e:
                print(f"物理距离计算出错: {e}")

        # 使用增强的显示函数
        img_with_distance = show_result_with_distance(meta['raw_img'].copy(), dets, detection_results, class_names, 0.3)
        cv2.imshow('det_with_distance', img_with_distance)

        # 也显示原始检测结果作为对比
        # detector.show(dets, meta, class_names, 0.3, wait=1)
        ch = cv2.waitKey(1)
        if ch == 27 or ch == ord('q') or ch == ord('Q'):
            break




# import cv2

# cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
# # 指定分辨率和编码格式
# cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
# cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
# cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)

# if not cap.isOpened():
#     print("摄像头打开失败")
#     exit()

# while True:
#     ret, frame = cap.read()
#     if not ret:
#         print("无法读取帧")
#         break

#     cv2.imshow("Camera", frame)
#     if cv2.waitKey(1) & 0xFF == ord('q'):
#         break

# cap.release()
# cv2.destroyAllWindows()


