import numpy as np
import os

class PixelConverter:
    # Constants
    IMGX_RANGE = [30, 1250]  # Example values, adjust as needed
    IMGY_RANGE = [400, 720]  # Example values, adjust as needed
    X_MIN = 0        # Example values, adjust as needed
    X_MAX = 82         # Example values, adjust as needed
    Y_MIN = -125        # Example values, adjust as needed
    Y_MAX = 125         # Example values, adjust as needed

    TABLE_WIDTH = IMGX_RANGE[1] - IMGX_RANGE[0] + 1
    TABLE_HEIGHT = IMGY_RANGE[1] - IMGY_RANGE[0] + 1


    def __init__(self, table_path):
        self.table_path = table_path
        self.table_data = None
        self._load_table()

    def _load_table(self):
        """Load the distance table from file."""
        if not os.path.exists(self.table_path):
            raise FileNotFoundError(f"无法打开距离表文件: {self.table_path}")

        # Read the binary file
        with open(self.table_path, 'rb') as f:
            data = f.read()

        # Reshape the data into a 2D array of (x, y) pairs
        data = np.frombuffer(data, dtype=np.uint8)
        self.table_data = data.reshape(-1, 2)

    def is_valid_pixel_coordinate(self, pixel_x, pixel_y):
        """Check if the pixel coordinates are within valid range."""
        return (self.IMGX_RANGE[0] <= pixel_x <= self.IMGX_RANGE[1] and
                self.IMGY_RANGE[0] <= pixel_y <= self.IMGY_RANGE[1])

    def map_value_back(self, value, min_val, max_val):
        """Map a value from [0, 255] range back to [min_val, max_val] range."""
        return min_val + (value / 255.0) * (max_val - min_val)

    def query_physical_location(self, pixel_x, pixel_y):
        """Query the physical location for given pixel coordinates."""
        if not self.is_valid_pixel_coordinate(pixel_x, pixel_y):
            raise ValueError(f"像素坐标超出范围！有效范围: X{self.IMGX_RANGE}, Y{self.IMGY_RANGE}")

        # Calculate index in the table
        row_offset = pixel_y - self.IMGY_RANGE[0]
        col_offset = pixel_x - self.IMGX_RANGE[0]
        index = row_offset * self.TABLE_WIDTH + col_offset

        if index >= len(self.table_data):
            raise ValueError(f"坐标 ({pixel_x}, {pixel_y}) 的索引 {index} 无效")

        x_value, y_value = self.table_data[index]

        # Check for invalid values
        if x_value == 0 and y_value == 0:
            print(f"警告：在像素位置 ({pixel_x},{pixel_y}) 检测到坐标 (0,0)")
            print("这可能表示该区域无效或未校准。")

        # Map values back to physical coordinates
        physical_x = self.map_value_back(x_value, self.X_MIN, self.X_MAX)
        physical_y = self.map_value_back(y_value, self.Y_MIN, self.Y_MAX)

        return physical_x, physical_y 