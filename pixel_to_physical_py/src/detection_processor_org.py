import yaml
import numpy as np
from dataclasses import dataclass
from typing import <PERSON>ple
from src.pixel_converter import PixelConverter

@dataclass
class BBox:
    x1: int
    y1: int
    x2: int
    y2: int

@dataclass
class DetectionResult:
    bbox: BBox
    label: str
    confidence: float
    physical_distance: float = 0.0
    left_distance: float = 0.0
    right_distance: float = 0.0

class DetectionProcessor:
    def __init__(self):
        self.size_ranges_config = None

    def set_size_ranges_config(self, config):
        """Set the size ranges configuration from YAML."""
        self.size_ranges_config = config

    def is_size_reasonable_for_label(self, label: str, max_size: float, min_size: float) -> bool:
        """Check if the object size is reasonable for its label."""
        try:
            object_config = self.size_ranges_config["objects"].get(label, self.size_ranges_config["default"])
            
            config_max_size = object_config["max_size"]
            config_min_size = object_config["min_size"]
            description = object_config["description"]

            if max_size > config_max_size or min_size < config_min_size:
                print(f"警告：{description}")
                return False
            return True
        except Exception as e:
            print(f"Error checking size ranges: {str(e)}")
            return False

    def calculate_target_size(self, bbox: BBox, converter: PixelConverter) -> Tuple[float, float, float, float]:
        """Calculate the target size and return size_y, size_x, z1, z2."""
        try:
            # Get physical coordinates for the corners
            x1, y1 = converter.query_physical_location(bbox.x1, bbox.y2)  # bottom left
            x2, y2 = converter.query_physical_location(bbox.x2, bbox.y2)  # bottom right
            # x3, y3 = converter.query_physical_location(bbox.x1, bbox.y1)  # top left

            # Print physical coordinates
            print(f"像素坐标 ({bbox.x1}, {bbox.y2}) -> 物理坐标 ({x1:.3f}, {y1:.3f})")
            print(f"像素坐标 ({bbox.x2}, {bbox.y2}) -> 物理坐标 ({x2:.3f}, {y2:.3f})")
            # print(f"像素坐标 ({bbox.x1}, {bbox.y1}) -> 物理坐标 ({x3:.3f}, {y3:.3f})")

            # Calculate lengths
            bottom_length = np.sqrt((x2 - x1)**2 + (y2 - y1)**2)
            # left_length = np.sqrt((x3 - x1)**2 + (y3 - y1)**2)

            print("----------------------------------------")
            print(f"下边长度: {bottom_length:.3f} cm")
            # print(f"左边宽度: {left_length:.3f} cm")
            print("----------------------------------------")

            return bottom_length, x1, x2

        except Exception as e:
            print(f"Error calculating target size: {str(e)}")
            return 0.0, 0.0, 0.0, 0.0

    def is_detection_reasonable(self, det: DetectionResult, table_path: str) -> Tuple[bool, float, float]:
        """Check if the detection result is reasonable."""
        if det is None:
            print("检测结果为空!")
            return False, 0.0, 0.0

        converter = PixelConverter(table_path)

        # Check if box is in image range
        if not converter.is_valid_pixel_coordinate(det.bbox.x1, det.bbox.y2) or \
           not converter.is_valid_pixel_coordinate(det.bbox.x2, det.bbox.y2):
            print(det.bbox.x1, det.bbox.y1, det.bbox.x2, det.bbox.y2)
            print("警告：检测框超出图像范围")
            return False, 0.0, 0.0

        # Check if box is too small
        box_area = (det.bbox.x2 - det.bbox.x1) * (det.bbox.y2 - det.bbox.y1)
        if box_area < 10:
            print(f"警告：检测框面积过小 ({box_area} 像素)")
            return False, 0.0, 0.0

        # Calculate target size
        size_y, z1, z2 = self.calculate_target_size(det.bbox, converter)
        if size_y == 0.0:
            print("无法计算目标尺寸")
            return False, 0.0, 0.0

        # Check if distance is reasonable
        # distance = abs(size_x)
        distance = z1
        if distance > PixelConverter.X_MAX:
            print(f"警告：目标距离过远 ({distance:.1f} cm)")
            return False, 0.0, 0.0

        # Print target size
        print(f"\n目标尺寸:")
        print(f"长度: {size_y:.3f} cm")
        # print(f"宽度: {size_x:.3f} cm")

        # Check size against label
        # max_size = max(size_x, size_y)
        # min_size = min(size_x, size_y)
        # if not self.is_size_reasonable_for_label(det.label, max_size, min_size):
        #     return False, 0.0, 0.0

        return True, z1, z2

    def process_detection_result(self, det: DetectionResult, table_path: str):
        """Process the detection result and update physical distances."""
        print("\n处理检测结果:")
        print(f"标签: {det.label}, 置信度: {det.confidence}")
        print("----------------------------------------")

        is_reasonable, z1, z2 = self.is_detection_reasonable(det, table_path)
        if not is_reasonable:
            print("检测结果不合理，跳过处理")
            return

        # Update detection result with physical distances
        det.left_distance = z1
        det.right_distance = z2
        det.physical_distance = (z1 + z2) / 2  # Use average of left and right distances 