import yaml
from src.detection_processor import DetectionProcessor, DetectionResult, BBox

def main():
    try:
        # Load configuration file
        with open("config/size_ranges.yaml", 'r') as f:
            config = yaml.safe_load(f)
        
        # Create detection processor instance
        processor = DetectionProcessor()
        processor.set_size_ranges_config(config)

        # Create detection result
        det = DetectionResult(
            # bbox=BBox(x1=130, y1=414, x2=603, y2=429),
            bbox=BBox(x1=209, y1=480, x2=1020, y2=550),

            label="bin",
            confidence=0.95
        )

        # Process detection result
        processor.process_detection_result(det, "config/distance_table")
        
        print(f"最终物理距离: {det.physical_distance:.2f} cm")
        print(f"左下角距离: {det.left_distance:.2f} cm")
        print(f"右下角距离: {det.right_distance:.2f} cm")

    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    main() 