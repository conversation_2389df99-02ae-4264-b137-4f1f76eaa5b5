version: 1.0.0
perspective_transform:
  enabled: true
  description: "透视变换配置，用于将图像转换为鸟瞰图"
  
  # 透视变换的四个角点坐标 [x, y]
  # 顺序：左上角、右上角、右下角、左下角
  corners:
    top_left: [100, 200]      # 左上角
    top_right: [1180, 200]    # 右上角
    bottom_right: [1180, 600] # 右下角
    bottom_left: [100, 600]   # 左下角
  
  # 输出图像尺寸
  output_size:
    width: 1280
    height: 720
  
  # 调试选项
  debug:
    enabled: false
    save_intermediate_images: false
    output_path: "debug_output"
    
  # 处理选项
  processing:
    interpolation_method: "linear"  # linear, cubic, nearest
    border_mode: "constant"         # constant, reflect, wrap, replicate
    border_value: [0, 0, 0]        # RGB值，当border_mode为constant时使用

# 去畸变处理配置
undistortion:
  enabled: true
  alpha: 0  # 0=裁剪所有无效像素, 1=保留所有像素
  
  # 调试选项
  debug:
    enabled: false
    save_undistorted_image: false
    output_path: "debug_output"

# 图像处理流水线配置
pipeline:
  # 处理步骤顺序
  steps:
    - "undistortion"      # 去畸变
    - "perspective"       # 透视变换
  
  # 如果某个步骤失败，是否继续后续步骤
  continue_on_failure: true
  
  # 是否显示中间处理结果
  show_intermediate_results: false
