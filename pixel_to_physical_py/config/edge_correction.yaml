# 边缘校正配置文件
edge_correction:
  enabled: true
  
  # 校正强度参数
  strength: 0.15  # 基础校正强度，范围 0.0-0.5
  
  # 图像参数
  image_width: 1280
  image_height: 720
  
  # 权重配置
  weights:
    x_direction: 0.8  # X方向（水平）权重，影响更大
    y_direction: 0.2  # Y方向（垂直）权重，影响较小
  
  # 分区校正（可选，用于更精细的校正）
  zone_correction:
    enabled: false
    zones:
      # 左边缘区域
      left_edge:
        x_range: [0, 200]
        correction_factor: 1.2
      # 右边缘区域  
      right_edge:
        x_range: [800, 1280]
        correction_factor: 1.2
      # 上边缘区域
      top_edge:
        y_range: [400, 500]
        correction_factor: 1.1
      # 下边缘区域
      bottom_edge:
        y_range: [620, 720]
        correction_factor: 1.1
        
  # 调试选项
  debug:
    print_corrections: true  # 是否打印校正信息
    min_correction_threshold: 1.05  # 最小校正阈值，低于此值不打印
    
  # 校正算法选择
  algorithm: "radial"  # "radial" 或 "linear" 或 "zone"
  
  # 径向校正参数（当algorithm为"radial"时使用）
  radial_correction:
    center_weight: 1.0    # 中心区域权重
    edge_weight: 1.3      # 边缘区域最大权重
    falloff_power: 2.0    # 衰减指数
